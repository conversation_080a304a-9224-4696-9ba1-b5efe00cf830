{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\venta_cuba111\\venta_cuba\\android\\app\\.cxx\\RelWithDebInfo\\5t5j3u3i\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\venta_cuba111\\venta_cuba\\android\\app\\.cxx\\RelWithDebInfo\\5t5j3u3i\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}