{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\venta_cuba111\\venta_cuba\\android\\app\\.cxx\\Debug\\34z3z2wq\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\venta_cuba111\\venta_cuba\\android\\app\\.cxx\\Debug\\34z3z2wq\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}